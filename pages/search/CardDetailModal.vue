<template>
  <Transition name="modal">
    <div
      v-if="modelValue"
      class="fixed inset-0 z-50 bg-black bg-opacity-40 flex justify-center items-end md:items-center"
      @click.self="$emit('update:modelValue', false)"
    >
      <!-- 主体弹窗 -->
      <div
        class="bg-white dark:bg-gray-800 text-gray-900 dark:text-white relative p-4 shadow-lg w-full md:w-[320px] md:mx-0 fixed md:relative bottom-0 md:bottom-auto rounded-t-xl md:rounded-xl transition-transform duration-300 md:rounded-xl rounded-b-none md:px-4"
      >
        <!-- 关闭按钮 -->
        <button
          class="absolute top-2 right-2 text-gray-500 bg-transparent hover:text-gray-700 dark:hover:text-gray-300"
          @click="$emit('update:modelValue', false)"
        >
          <img src="~/assets/image/Close-small.svg" alt="" />
        </button>

        <!-- 顶部用户信息 -->
        <div class="flex items-center gap-3 mb-4">
          <img :src="user.avatar" alt="avatar" class="w-12 h-12 rounded-full" />
          <div class="text-left">
            <p class="text-base font-semibold">{{ user.name }}</p>
            <p class="text-sm text-gray-500 dark:text-gray-400">{{ user.title }}</p>
          </div>
        </div>

        <!-- 下方按钮区域（竖排） -->
        <div class="flex flex-col gap-3">
          <button class="btn-analyze" @click="handleAnalyzeAction">
            <img src="~/assets/image/analysis.svg" alt="Analyze" class="btn-icon btn-icon-dark" />
            <img src="~/assets/image/analysis2.svg" alt="Analyze" class="btn-icon btn-icon-light" />
            Analyze
          </button>

          <button class="btn-network" @click="showNetworkModal = true">
            <img
              src="~/assets/image/button-group.svg"
              alt="Network"
              class="btn-icon btn-icon-dark"
            />
            <img
              src="~/assets/image/button-group2.svg"
              alt="Network"
              class="btn-icon btn-icon-light"
            />
            Network
          </button>
          <button class="btn-see-another">
            <img src="~/assets/image/cv1.svg" alt="Refresh" class="btn-icon btn-icon-light" />
            <img src="~/assets/image/refresh2.svg" alt="Refresh" class="btn-icon btn-icon-dark" />
            Resume/CV
          </button>
        </div>
      </div>
      <!-- Network模态框组件 -->
      <NetworkModal
        v-if="props.user"
        :show="showNetworkModal"
        :currentCandidate="props.user"
        @update:show="showNetworkModal = $event"
      />
    </div>
  </Transition>
</template>

<script setup>
  import { ref } from 'vue'
  import NetworkModal from '@/components/SearchCard/NetworkModal.vue'
  import { useRouter } from 'vue-router'
  const router = useRouter()

  const props = defineProps({
    modelValue: Boolean,
    user: {
      type: Object,
      required: true,
    },
  })

  const showNetworkModal = ref(false)

  const emit = defineEmits(['update:modelValue'])

  // --- Actions ---
  const handleAnalyzeAction = () => {
    console.log(props.user)
    if (props.user) {
      // 根据author_ids格式判断数据类型，然后选择对应的分析页面
      if (props.user?.id) {
        // OpenReview ID，使用scholar分析页面
        router.push(`/report?query=${props.user.id}`)
      } else {
        // GitHub login，使用github分析页面
        router.push(`/github?query=${props.user.id}`)
      }
    }
  }
</script>

<style>
  /* 按钮图标控制 */
  .btn-icon {
    width: 24px;
    height: 24px;
  }

  .btn-icon-dark {
    display: none;
  }

  .dark .btn-icon-light {
    display: none;
  }

  .dark .btn-icon-dark {
    display: inline-block;
  }

  .btn-analyze,
  .btn-network,
  .btn-see-another {
    height: 40px;
    border-radius: 8px;
    font-family: 'Poppins', sans-serif;
    font-weight: 500;
    font-size: 14px;
    line-height: 100%;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    white-space: nowrap;
    box-sizing: border-box;
    border: 1px solid #000000;
  }

  .btn-analyze,
  .btn-network {
    background-color: transparent;
    color: #000000;
    /* padding: 16px 32px; -- width/height and flex handles this */
  }

  .btn-analyze:hover,
  .btn-network:hover {
    background-color: #ccc;
  }

  .btn-see-another {
    background-color: transparent;
    color: #000000;
    /* padding: 16px 20px; */
  }

  .btn-see-another:hover {
    background-color: #f5f5f5;
  }

  .dark .btn-see-another,
  .dark .btn-analyze,
  .dark .btn-network {
    background-color: transparent;
    border: 1px solid #323232;
    color: #faf9f5;
  }

  .dark .btn-see-another:hover,
  .dark .btn-analyze:hover,
  .dark .btn-network:hover {
    background-color: rgba(50, 50, 50, 0.1);
  }

  @media (max-width: 768px) {
    .modal-enter-active,
    .modal-leave-active {
      transition: all 0.3s ease;
    }

    .modal-enter-from {
      transform: translateY(100%);
      opacity: 0;
    }

    .modal-enter-to {
      transform: translateY(0);
      opacity: 1;
    }

    .modal-leave-from {
      transform: translateY(0);
      opacity: 1;
    }

    .modal-leave-to {
      transform: translateY(100%);
      opacity: 0;
    }
  }
</style>
