// import { get } from "../utils/request"
import { get, del, post, put, getFunc } from "../utils/request"
import type { TalentRes, GraphParams, GraphResponse, NetworkParams, NetworkResponse } from "./types"

export const getTalentsData = async (count: number = 3, options?: object): Promise<{ data: TalentRes }> => {
  const apiBaseUrl = getApiBaseUrl()
  const params = new URLSearchParams({
    count: count.toString(),
    ...(options || {})
  })
  
  const fullUrl = `${apiBaseUrl}/api/top-talents?${params.toString()}`

  try {
    const response = await fetch(fullUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // 首先检查响应状态
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
    }

    // 读取响应数据
    const result = await response.json();

    return {
      data: result
    }
  } catch(err) {
    console.log('getTalentsData err', err);
    throw err;
  }
}

// 获取学者信息;
export interface DemoInfo {
  countries: {
    code: string
    name_en: string
  }[]
}

export const getDemoInfo = () => get<DemoInfo>('/api/demo-form/info')

export interface DemoRequest {
  email: string
  affiliation: string
  country: string
  job_title: string
  contact_reason: string
  additional_details?: string
  marketing_consent: boolean
}

export const getApiBaseUrl = () => {
  // 直接返回硬编码的 API 基础URL
  return 'https://api.dinq.io'
}

export const submitDemoRequest = async (SERVER_URL: string, data: DemoRequest, options: Record<string, any>) => {

  let fullUrl = SERVER_URL

  // 如果SERVER_URL是相对路径（以/开头），则添加域名
  if (SERVER_URL.startsWith('/')) {
    const apiBaseUrl = getApiBaseUrl()
    fullUrl = `${apiBaseUrl}${SERVER_URL}`
  }

  const response = await fetch(fullUrl, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      ...options.headers
    },
    body: JSON.stringify(data),
  });

  // 首先检查响应状态
  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  // 读取响应数据
  const result = await response.json();  

  if (result.success) {
    return {
      data: {
          success: result?.success || false
      }
    }
  }else {
    return {
      data: {
        success: false
      }
    }
  }

}


// 激活码提交; /api/activation-codes/use
export const submitActivationCode = async (SERVER_URL: string, data: { code: string }, options: Record<string, any>) => {
  let fullUrl = SERVER_URL

  // 如果SERVER_URL是相对路径（以/开头），则添加域名
  if (SERVER_URL.startsWith('/')) {
    const apiBaseUrl = getApiBaseUrl()
    fullUrl = `${apiBaseUrl}${SERVER_URL}`
  }

  try {
    const response = await fetch(fullUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      body: JSON.stringify(data),
    });
  
    // 首先检查响应状态
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
  
    // 读取响应数据
    const result = await response.json();
  
    console.log('response', result);
  
    if (result.success) {
      return {
        data: {
            success: result?.success || false
        }
      }
    } else {
      return {
        data: {
          success: false
        }
      }
    }
  
  } catch(err) {
    console.log('submitActivationCode err', err);
    return {
      data: {
        success: false
      }
    }
  }
}



interface IJoinWaitingList {
  email: string,
  name?: string,
  organization?: string,
  job_title?: string,
  reason?: string,
  additional_field1?: string,
  additional_field2?: string
}
// 加入等待列表;
export const joinWaitingList = async (SERVER_URL: string, data: IJoinWaitingList, options: Record<string, any>) => {
  let fullUrl = SERVER_URL

  // 如果SERVER_URL是相对路径（以/开头），则添加域名
  if (SERVER_URL.startsWith('/')) { 
    const apiBaseUrl = getApiBaseUrl()
    fullUrl = `${apiBaseUrl}${SERVER_URL}`
  }

  try {
    const response = await fetch(fullUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      body: JSON.stringify(data),
    });

    // 首先检查响应状态
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    // 读取响应数据
    const result = await response.json();

    console.log('response', result);
    
    if (result.success) {
      return {
        data: {
          success: result?.success || false
        }
      }
    } else {
      return {
        data: {
          success: false
        }
      }
    }
  } catch(err) {
    console.log('joinWaitingList err', err);
    return {
      data: {
        success: false
      }
    }
  } 
}


// 获取当前用户的等待状态， 判断是否已经介入等待列表;
export const getWaitingListStatus = async (SERVER_URL: string, options: Record<string, any>) => {
  let fullUrl = SERVER_URL

  // 如果SERVER_URL是相对路径（以/开头），则添加域名
  if (SERVER_URL.startsWith('/')) {
    const apiBaseUrl = getApiBaseUrl()
    fullUrl = `${apiBaseUrl}${SERVER_URL}`
  }

  try {
    const response = await fetch(fullUrl, {
      method: 'GET',
      headers: {
        ...options.headers
      },
    });

    // 首先检查响应状态
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    // 读取响应数据
    const result = await response.json();
    
    if (result.success) {
      return {
        data: {
          success: result?.success || false,
          data: result || {}
        }
      }
    } else {
      return {
        data: {
          success: false
        }
      }
    }
  } catch(err) {
    console.log('getWaitingListStatus err', err);
    return {
      data: {
        success: false
      }
    }
  }
}

// GitHub API interfaces
export interface GitHubAnalyzeRequest {
  username: string;
}

export interface GitHubUser {
  name: string;
  login: string;
  avatarUrl: string;
  bio: string;
  company: string;
  location: string;
  createdAt: string;
  tags: string[];
  url: string;
  id: string;
}

export interface GitHubOverview {
  work_experience: number;
  stars: number;
  repositories: number;
  pull_requests: number;
  issues: number;
  additions: number;
  deletions: number;
}

export interface GitHubValuationAndLevel {
  level: string;
  salary_range: string;
  total_compensation: string;
  reasoning: string;
}

export interface GitHubRoleModel {
  name: string;
  github: string;
  similarity_score: number;
  reason: string;
}

export interface GitHubFeatureProject {
  name: string;
  nameWithOwner: string;
  description: string;
  url: string;
  stargazerCount: number;
  forkCount: number;
  tags: string[];
  contributors: number;
  used_by: number;
  monthly_trending: number;
  owner: {
    avatarUrl: string;
  };
}

export interface GitHubMostValuablePR {
  title: string;
  url: string;
  repository: string;
  additions: number;
  deletions: number;
  impact: string;
  reason: string;
}

export interface GitHubProjectInfo {
  repository: {
    name: string;
    url: string;
    description: string;
    stargazerCount: number;
    owner: {
      avatarUrl: string;
    };
  };
  pull_requests: number;
}

export interface GitHubCodeContribution {
  total: number;
  languages: Record<string, number>;
}

export interface GitHubActivityData {
  contributions: number;
  pull_requests: number;
  issues: number;
  comments: number;
}

export interface GitHubUsageInfo {
  remaining_uses: number | null;
  total_usage: number;
  limit: number | null;
  period_days: number;
}

export interface GitHubAnalysisData {
  user: GitHubUser;
  overview: GitHubOverview;
  valuation_and_level: GitHubValuationAndLevel;
  role_model: GitHubRoleModel;
  feature_project: GitHubFeatureProject;
  most_valuable_pull_request: GitHubMostValuablePR;
  top_projects: GitHubProjectInfo[];
  code_contribution: GitHubCodeContribution;
  activity: Record<string, GitHubActivityData>;
  roast: string;
}

export interface GitHubAnalyzeResponse {
  success: boolean;
  username: string;
  data: GitHubAnalysisData;
  usage_info: GitHubUsageInfo;
  error?: string;
}

export interface GitHubStatsResponse {
  user_id: string;
  github_analysis_stats: Record<string, number>;
  limits: {
    monthly_limit: number;
    period_days: number;
  };
}

// GitHub API functions
export const analyzeGitHubUser = async (
  SERVER_URL: string, 
  data: GitHubAnalyzeRequest, 
  options: Record<string, any>
): Promise<{ data: GitHubAnalyzeResponse }> => {
  let fullUrl = SERVER_URL

  // 如果SERVER_URL是相对路径（以/开头），则添加域名
  if (SERVER_URL.startsWith('/')) {
    const apiBaseUrl = getApiBaseUrl()
    fullUrl = `${apiBaseUrl}${SERVER_URL}`
  }

  try {
    const response = await fetch(fullUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      body: JSON.stringify(data),
    });

    // 首先检查响应状态
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
    }

    // 读取响应数据
    const result = await response.json();

    return {
      data: result
    }
  } catch(err) {
    console.log('analyzeGitHubUser err', err);
    throw err;
  }
}

export const getGitHubStats = async (
  SERVER_URL: string, 
  options: Record<string, any>
): Promise<{ data: GitHubStatsResponse }> => {
  let fullUrl = SERVER_URL

  // 如果SERVER_URL是相对路径（以/开头），则添加域名
  if (SERVER_URL.startsWith('/')) {
    const apiBaseUrl = getApiBaseUrl()
    fullUrl = `${apiBaseUrl}${SERVER_URL}`
  }

  try {
    const response = await fetch(fullUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
    });

    // 首先检查响应状态
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
    }

    // 读取响应数据
    const result = await response.json();

    return {
      data: result
    }
  } catch(err) {
    console.log('getGitHubStats err', err);
    throw err;
  }
}

// GitHub Dev Pioneers API interfaces
export interface GitHubDeveloper {
  name: string
  Job: string
  image: string
  Company: string
  github: string
  has_github: boolean
  area?: string
  famous_work?: string
  link?: string
  linkedin?: string
  twitter?: string
  personal_page?: string
  has_image?: boolean
  has_linkedin?: boolean
}

export interface GitHubDevPioneersResponse {
  success: boolean
  count: number
  total_available: number
  total_in_database: number
  pioneers: GitHubDeveloper[]
  filters_applied: Record<string, any>
  metadata: {
    available_areas: string[]
    available_companies: string[]
    csv_file_path: string
  }
}

export const getGitHubDevPioneers = async (
  count: number = 3,
  options: Record<string, any> = {}
): Promise<{ data: GitHubDevPioneersResponse }> => {
  const params = new URLSearchParams({
    count: count.toString(),
    random: 'true',
    ...options
  })

  const fullUrl = `https://api.dinq.io/api/github/dev-pioneers?${params.toString()}`

  try {
    const response = await fetch(fullUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // 首先检查响应状态
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
    }

    // 读取响应数据
    const result = await response.json();

    return {
      data: result
    }
  } catch(err) {
    console.log('getGitHubDevPioneers err', err);
    throw err;
  }
}

// 构建完整的Graph API URL - 统一使用代理方式
const getGraphApiUrl = () => {
  // 由于 graph 接口不支持跨域，统一使用代理路径
  return '/api/v1/graph'
}

// Graph API - 获取用户网络关系
export const getGraph = async (params: GraphParams): Promise<GraphResponse> => {
  // 构建查询参数
  const searchParams = new URLSearchParams();
  searchParams.append('user', params.user);
  searchParams.append('type', params.type);

  // 使用动态URL构建
  const baseUrl = getGraphApiUrl()
  const url = `${baseUrl}?${searchParams.toString()}`;
  console.log(url)

  try {
    // 创建AbortController用于超时控制
    const controller = new AbortController();
    const timeoutId = setTimeout(() => {
      controller.abort();
    }, 30000); // 30秒超时
    
    const response = await fetch(url, {
    method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      },
      signal: controller.signal,
    });
    
    // 清除超时定时器
    clearTimeout(timeoutId);
    
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`HTTP error! Status: ${response.status}, Message: ${errorText}`);
    }
    
    const data = await response.json();
    return data;
  } catch (error: any) {
    // 特殊处理不同类型的错误
    if (error?.name === 'AbortError') {
      throw new Error('Request timeout after 30 seconds');
    } else if (error?.name === 'TypeError') {
      throw new Error('Network error: ' + error.message);
    }
    
    throw error;
  }
};

// 构建完整的Network API URL - 统一使用代理方式
const getNetworkApiUrl = () => {
  // 使用代理路径
  return '/api/v1/talent/network'
}

// Network API - 获取用户网络关系（新版本，支持Firebase认证）
export const getNetwork = async (params: NetworkParams, userUid: string): Promise<NetworkResponse> => {
  // 构建查询参数
  const searchParams = new URLSearchParams();
  searchParams.append('id', params.id);
  searchParams.append('builder', params.builder);
  if (params.group) {
    searchParams.append('group', params.group);
  }

  // 使用动态URL构建
  const baseUrl = getNetworkApiUrl()
  const url = `${baseUrl}?${searchParams.toString()}`;
  console.log('Network API URL:', url)

  try {
    // 创建AbortController用于超时控制
    const controller = new AbortController();
    const timeoutId = setTimeout(() => {
      controller.abort();
    }, 30000); // 30秒超时

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${userUid}` // Firebase认证
      },
      signal: controller.signal,
    });

    // 清除超时定时器
    clearTimeout(timeoutId);

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`HTTP error! Status: ${response.status}, Message: ${errorText}`);
    }

    const data = await response.json();
    return data;
  } catch (error: any) {
    // 特殊处理不同类型的错误
    if (error?.name === 'AbortError') {
      throw new Error('Request timeout after 30 seconds');
    } else if (error?.name === 'TypeError') {
      throw new Error('Network error: ' + error.message);
    }

    throw error;
  }
};